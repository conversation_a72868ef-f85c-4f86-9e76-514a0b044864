#!/usr/bin/env python3
"""
MessPHP Utilities

Additional utility functions for MessPHP decryption and analysis.
"""

import re
import base64
import hashlib
from typing import Dict, List, Optional, Tuple


class MessPHPAnalyzer:
    """Analyzer for MessPHP protected files"""
    
    def __init__(self):
        self.variable_patterns = {
            'obfuscated_vars': re.compile(r'\$m[a-f0-9]{32}'),
            'ascii_decoder': re.compile(r'xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii'),
            'xtea_class': re.compile(r'class\s+Xtea', re.IGNORECASE),
            'eval_call': re.compile(r'eval\s*\('),
            'encrypted_payload': re.compile(r'"([A-Za-z0-9+/=]{100,})"')
        }
    
    def analyze_file(self, content: str) -> Dict:
        """Analyze MessPHP file and return detailed information"""
        analysis = {
            'is_messphp': False,
            'version': None,
            'obfuscated_variables': [],
            'ascii_encoded_strings': [],
            'encrypted_payloads': [],
            'anti_debug_measures': [],
            'complexity_score': 0
        }
        
        # Check for MessPHP signature
        if 'This file was protected by MessPHP' in content:
            analysis['is_messphp'] = True
            version_match = re.search(r'MessPHP v([\d.]+)', content)
            if version_match:
                analysis['version'] = version_match.group(1)
        
        # Find obfuscated variables
        analysis['obfuscated_variables'] = list(set(
            self.variable_patterns['obfuscated_vars'].findall(content)
        ))
        
        # Find ASCII encoded strings
        ascii_calls = re.findall(
            r'xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii\([\'"](\d+)[\'"]\)', 
            content
        )
        for ascii_code in ascii_calls:
            decoded = self._decode_ascii_string(ascii_code)
            if decoded:
                analysis['ascii_encoded_strings'].append({
                    'encoded': ascii_code,
                    'decoded': decoded
                })
        
        # Find encrypted payloads
        analysis['encrypted_payloads'] = self.variable_patterns['encrypted_payload'].findall(content)
        
        # Check for anti-debugging measures
        anti_debug_patterns = [
            (r'preg_match.*print.*echo', 'Print/Echo detection'),
            (r'while\s*\(\s*0x\w+\s*!=\s*0x\w+\s*\)', 'Infinite loop trap'),
            (r'file_get_contents\s*\(\s*__FILE__\s*\)', 'Self-inspection'),
            (r'strpos.*base64_decode', 'Base64 content checking')
        ]
        
        for pattern, description in anti_debug_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                analysis['anti_debug_measures'].append(description)
        
        # Calculate complexity score
        analysis['complexity_score'] = self._calculate_complexity(content, analysis)
        
        return analysis
    
    def _decode_ascii_string(self, ascii_codes: str) -> str:
        """Decode ASCII codes string to characters"""
        try:
            padded_len = ((len(ascii_codes) + 2) // 3) * 3
            padded_codes = ascii_codes.zfill(padded_len)
            
            result = ""
            for i in range(0, len(padded_codes), 3):
                code = int(padded_codes[i:i+3])
                if 0 <= code <= 255:
                    result += chr(code)
            
            return result
        except (ValueError, OverflowError):
            return ""
    
    def _calculate_complexity(self, content: str, analysis: Dict) -> int:
        """Calculate obfuscation complexity score (0-100)"""
        score = 0
        
        # Base MessPHP detection
        if analysis['is_messphp']:
            score += 20
        
        # Obfuscated variables
        score += min(len(analysis['obfuscated_variables']) * 2, 20)
        
        # ASCII encoded strings
        score += min(len(analysis['ascii_encoded_strings']) * 3, 15)
        
        # Encrypted payloads
        score += min(len(analysis['encrypted_payloads']) * 10, 20)
        
        # Anti-debug measures
        score += min(len(analysis['anti_debug_measures']) * 5, 15)
        
        # Code density (high ratio of non-whitespace characters)
        non_whitespace = len(re.sub(r'\s', '', content))
        total_chars = len(content)
        if total_chars > 0:
            density = non_whitespace / total_chars
            if density > 0.8:
                score += 10
        
        return min(score, 100)
    
    def extract_variable_mappings(self, content: str) -> Dict[str, str]:
        """Extract mappings of obfuscated variables to their decoded values"""
        mappings = {}
        
        # Find variable assignments with ASCII decoding
        pattern = r'\$([a-f0-9]{32})\s*=\s*xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii\([\'"](\d+)[\'"]\)'
        matches = re.findall(pattern, content)
        
        for var_hash, ascii_code in matches:
            decoded = self._decode_ascii_string(ascii_code)
            if decoded:
                mappings[f'${var_hash}'] = decoded
        
        return mappings


class MessPHPValidator:
    """Validator for MessPHP files and decryption results"""
    
    @staticmethod
    def validate_php_syntax(code: str) -> Tuple[bool, Optional[str]]:
        """Basic PHP syntax validation"""
        try:
            # Check for basic PHP structure
            if not code.strip():
                return False, "Empty code"
            
            # Check for PHP opening tag
            if not re.search(r'<\?php', code, re.IGNORECASE):
                return False, "Missing PHP opening tag"
            
            # Check for balanced braces
            open_braces = code.count('{')
            close_braces = code.count('}')
            if open_braces != close_braces:
                return False, f"Unbalanced braces: {open_braces} open, {close_braces} close"
            
            # Check for balanced parentheses
            open_parens = code.count('(')
            close_parens = code.count(')')
            if open_parens != close_parens:
                return False, f"Unbalanced parentheses: {open_parens} open, {close_parens} close"
            
            # Check for common PHP syntax errors
            error_patterns = [
                (r'\$\$+', "Double dollar signs detected"),
                (r'[^\\];[\s]*\n[\s]*[a-zA-Z]', "Possible missing semicolon"),
                (r'function\s+\w+\s*\([^)]*\)\s*[^{]', "Function without opening brace")
            ]
            
            for pattern, error_msg in error_patterns:
                if re.search(pattern, code):
                    return False, error_msg
            
            return True, None
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    @staticmethod
    def validate_messphp_structure(content: str) -> Tuple[bool, List[str]]:
        """Validate MessPHP file structure"""
        issues = []
        
        required_elements = [
            (r'class\s+Xtea', "Missing Xtea class"),
            (r'function\s+xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii', "Missing ASCII decoder function"),
            (r'block_decrypt', "Missing block_decrypt method"),
            (r'eval\s*\(', "Missing eval call")
        ]
        
        for pattern, error_msg in required_elements:
            if not re.search(pattern, content, re.IGNORECASE):
                issues.append(error_msg)
        
        return len(issues) == 0, issues
    
    @staticmethod
    def estimate_decryption_success(original: str, decrypted: str) -> float:
        """Estimate the success rate of decryption (0.0 to 1.0)"""
        if not decrypted or not decrypted.strip():
            return 0.0
        
        score = 0.0
        
        # Check for PHP opening tag
        if re.search(r'<\?php', decrypted, re.IGNORECASE):
            score += 0.3
        
        # Check for common PHP constructs
        php_constructs = [
            r'\$\w+\s*=',  # Variable assignments
            r'function\s+\w+',  # Function definitions
            r'if\s*\(',  # If statements
            r'echo\s+',  # Echo statements
            r'include\s+',  # Include statements
            r'class\s+\w+',  # Class definitions
        ]
        
        construct_score = 0
        for pattern in php_constructs:
            if re.search(pattern, decrypted, re.IGNORECASE):
                construct_score += 1
        
        score += min(construct_score / len(php_constructs), 0.4)
        
        # Check for readable text vs gibberish
        readable_chars = len(re.findall(r'[a-zA-Z0-9\s]', decrypted))
        total_chars = len(decrypted)
        if total_chars > 0:
            readability = readable_chars / total_chars
            score += min(readability, 0.3)
        
        return min(score, 1.0)


def create_decryption_report(original_file: str, analysis: Dict, decrypted_code: str, 
                           success_rate: float) -> str:
    """Create a detailed decryption report"""
    report = f"""
MessPHP Decryption Report
========================

File: {original_file}
Analysis Date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

File Analysis:
--------------
MessPHP Protected: {'Yes' if analysis['is_messphp'] else 'No'}
Version: {analysis.get('version', 'Unknown')}
Complexity Score: {analysis['complexity_score']}/100

Obfuscation Details:
-------------------
Obfuscated Variables: {len(analysis['obfuscated_variables'])}
ASCII Encoded Strings: {len(analysis['ascii_encoded_strings'])}
Encrypted Payloads: {len(analysis['encrypted_payloads'])}
Anti-Debug Measures: {len(analysis['anti_debug_measures'])}

Anti-Debug Measures Detected:
{chr(10).join(f"- {measure}" for measure in analysis['anti_debug_measures']) if analysis['anti_debug_measures'] else "None"}

Decryption Results:
------------------
Success Rate: {success_rate:.1%}
Decrypted Code Length: {len(decrypted_code)} characters
PHP Syntax Valid: {'Yes' if MessPHPValidator.validate_php_syntax(decrypted_code)[0] else 'No'}

ASCII Decoded Strings:
---------------------
"""
    
    for item in analysis['ascii_encoded_strings'][:10]:  # Show first 10
        report += f"'{item['encoded']}' -> '{item['decoded']}'\n"
    
    if len(analysis['ascii_encoded_strings']) > 10:
        report += f"... and {len(analysis['ascii_encoded_strings']) - 10} more\n"
    
    return report
