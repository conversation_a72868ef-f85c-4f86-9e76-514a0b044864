# MessPHP Decryption Tool

A comprehensive Python tool for decrypting MessPHP obfuscated/encrypted PHP files. This tool is based on the tutorial found at [drubicza/tutorials](https://github.com/drubicza/tutorials/blob/master/reversing/tutorial-dekripsi_proteksi_messphp.md).

## Features

- **Complete XTEA Decryption**: Implements the XTEA algorithm used by MessPHP
- **Automatic Detection**: Identifies MessPHP protected files automatically
- **Multiple Output Formats**: Support for stdout, file, or directory output
- **Detailed Analysis**: Provides comprehensive analysis of MessPHP protection mechanisms
- **Batch Processing**: Process multiple files at once
- **Error Handling**: Robust error handling for various edge cases
- **Validation**: Built-in PHP syntax validation for decrypted code
- **Reporting**: Generate detailed decryption reports

## Installation

### Requirements

- Python 3.6 or higher
- No external dependencies required (uses only Python standard library)

### Setup

1. Clone or download the repository:
```bash
git clone <repository-url>
cd messphp-decryptor
```

2. Make the script executable (Linux/macOS):
```bash
chmod +x messphp_decryptor.py
```

3. Run tests to verify installation:
```bash
python test_messphp_decryptor.py
```

## Usage

### Basic Usage

```bash
# Decrypt a single file and print to stdout
python messphp_decryptor.py encrypted_file.php

# Decrypt and save to a specific file
python messphp_decryptor.py encrypted_file.php -o decrypted_file.php

# Decrypt multiple files to a directory
python messphp_decryptor.py *.php -o decrypted/
```

### Advanced Usage

```bash
# Check if files are MessPHP protected
python messphp_decryptor.py file1.php file2.php --check

# Analyze MessPHP protection details
python messphp_decryptor.py encrypted_file.php --analyze

# Generate detailed decryption report
python messphp_decryptor.py encrypted_file.php --report -o report.txt

# Force decryption even if file doesn't look like MessPHP
python messphp_decryptor.py suspicious_file.php --force

# Use a specific decryption key
python messphp_decryptor.py encrypted_file.php --key "custom_key"

# Verbose output for debugging
python messphp_decryptor.py encrypted_file.php -v
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `input` | Input MessPHP file(s) to decrypt |
| `-o, --output` | Output file or directory |
| `--check` | Only check if files are MessPHP protected |
| `--analyze` | Analyze MessPHP protection details |
| `--report` | Generate detailed decryption report |
| `-v, --verbose` | Enable verbose output |
| `--force` | Force decryption even if file doesn't look like MessPHP |
| `--key` | Specify decryption key manually |

## How It Works

### MessPHP Protection Mechanism

MessPHP uses several obfuscation and encryption techniques:

1. **Variable Name Obfuscation**: All variable names are replaced with MD5 hashes
2. **String Encoding**: Strings are encoded as ASCII codes using a custom function
3. **XTEA Encryption**: The main payload is encrypted using the XTEA algorithm
4. **Anti-Debugging**: Contains measures to prevent analysis and debugging
5. **Self-Inspection**: Checks its own code for modifications

### Decryption Process

The tool follows these steps to decrypt MessPHP files:

1. **Detection**: Identifies MessPHP protected files by looking for signatures
2. **Analysis**: Extracts obfuscated variables, encoded strings, and encrypted payloads
3. **Key Extraction**: Attempts to find the decryption key from the code
4. **Payload Extraction**: Locates the base64-encoded encrypted payload
5. **XTEA Decryption**: Decrypts the payload using the XTEA algorithm
6. **Code Cleaning**: Formats and validates the decrypted PHP code

### Key Components

- **XTEADecryptor**: Implements the XTEA decryption algorithm
- **MessPHPDecryptor**: Main decryption logic and file handling
- **MessPHPAnalyzer**: Analyzes MessPHP protection mechanisms
- **MessPHPValidator**: Validates decrypted code and estimates success rates

## Examples

### Example 1: Basic Decryption

```bash
$ python messphp_decryptor.py sample_messphp.php
<?php
error_reporting(0);
include'vendor/config.php';
session_start();

if ($_SESSION['user'] != "") {
    header("location: $account_is_onn");
}

$pass = $_POST['password'];

if (isset($_POST['submit'])) {
    if ($pass == $password) {
        session_start();
        $_SESSION['user'] = $pass;
        header("location: $account_is_onn");
    } else {
        echo "<script>alert('".$wrong_password_Error."');</script>";
    }
}
```

### Example 2: Analysis Report

```bash
$ python messphp_decryptor.py sample_messphp.php --analyze
Analysis for sample_messphp.php:
MessPHP Protected: Yes
Version: 1.0
Complexity Score: 85/100
Obfuscated Variables: 5
ASCII Encoded Strings: 3
Encrypted Payloads: 1
Anti-Debug Measures: 2
Anti-Debug Measures:
  - Print/Echo detection
  - Self-inspection
```

### Example 3: Batch Processing

```bash
$ python messphp_decryptor.py *.php -o decrypted/ -v
Processing: file1.php
Decrypted content saved to: decrypted/decrypted_file1.php
Processing: file2.php
Decrypted content saved to: decrypted/decrypted_file2.php
Processing: file3.php
Warning: 'file3.php' doesn't appear to be MessPHP protected. Use --force to decrypt anyway.

Decryption completed: 2/3 files processed successfully
```

## Troubleshooting

### Common Issues

1. **"Could not find encrypted payload"**
   - The file might not be MessPHP protected
   - Try using `--force` flag
   - Check if the file is corrupted

2. **"Could not determine decryption key"**
   - Try specifying a key manually with `--key`
   - The MessPHP variant might use a different key extraction method

3. **"Decryption failed"**
   - The key might be incorrect
   - The file might be corrupted or modified
   - Try different common keys

4. **Invalid PHP syntax in output**
   - The decryption might have partially failed
   - Check the original file integrity
   - Some MessPHP variants might need manual adjustments

### Debug Mode

Use the `-v` flag for verbose output to see detailed processing information:

```bash
python messphp_decryptor.py encrypted_file.php -v
```

### Testing

Run the test suite to verify functionality:

```bash
python test_messphp_decryptor.py
```

## Limitations

- Only supports MessPHP v1.0 and similar variants
- Some heavily modified MessPHP variants might not be supported
- Manual key specification might be required for some files
- Anti-debugging measures might prevent decryption in some environments

## Security Notice

This tool is intended for educational purposes and legitimate security research only. Users are responsible for ensuring they have proper authorization before using this tool on any files.

## Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is released under the MIT License. See LICENSE file for details.

## Acknowledgments

- Based on the tutorial by [drubicza](https://github.com/drubicza/tutorials)
- XTEA algorithm implementation
- MessPHP obfuscation analysis techniques
