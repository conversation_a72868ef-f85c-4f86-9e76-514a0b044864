#!/usr/bin/env python3
"""
Test Suite for MessPHP Decryptor

This script tests the MessPHP decryption functionality with various test cases.
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, mock_open

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from messphp_decryptor import MessPHPDecryptor, XTEADecryptor
from messphp_utils import MessPHPAnalyzer, MessPHPValidator


class TestXTEADecryptor(unittest.TestCase):
    """Test cases for XTEA decryption"""
    
    def setUp(self):
        self.xtea = XTEADecryptor("test_key")
    
    def test_key_setup(self):
        """Test key setup with different key types"""
        # String key
        xtea1 = XTEADecryptor("test")
        self.assertEqual(len(xtea1.key), 4)
        
        # List key
        xtea2 = XTEADecryptor([1, 2, 3, 4])
        self.assertEqual(xtea2.key, [1, 2, 3, 4])
        
        # Empty key
        xtea3 = XTEADecryptor("")
        self.assertEqual(xtea3.key, [0, 0, 0, 0])
    
    def test_32bit_operations(self):
        """Test 32-bit arithmetic operations"""
        # Test addition with overflow
        result = self.xtea._add(0x7FFFFFFF, 1)
        self.assertEqual(result, -0x80000000)
        
        # Test right shift
        result = self.xtea._rshift(0x80000000, 1)
        self.assertTrue(isinstance(result, int))
    
    def test_string_conversion(self):
        """Test string to longs and back conversion"""
        test_string = "test"
        longs = self.xtea._str_to_longs(test_string)
        self.assertTrue(isinstance(longs, list))
        self.assertTrue(all(isinstance(x, int) for x in longs))
        
        # Convert back
        result = self.xtea._longs_to_str(longs)
        self.assertTrue(test_string in result)


class TestMessPHPDecryptor(unittest.TestCase):
    """Test cases for MessPHP decryption"""
    
    def setUp(self):
        self.decryptor = MessPHPDecryptor()
    
    def test_ascii_string_decoding(self):
        """Test ASCII string decoding function"""
        # Test "Xtea" -> "088116101097"
        result = self.decryptor._decode_ascii_string("088116101097")
        self.assertEqual(result, "Xtea")
        
        # Test "trim" -> "116114105109"
        result = self.decryptor._decode_ascii_string("116114105109")
        self.assertEqual(result, "trim")
        
        # Test "encrypt" -> "101110099114121112116"
        result = self.decryptor._decode_ascii_string("101110099114121112116")
        self.assertEqual(result, "encrypt")
    
    def test_messphp_detection(self):
        """Test MessPHP file detection"""
        # Positive test
        messphp_content = """
        /* This file was protected by MessPHP v1.0 */
        class Xtea {
            function xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii() {}
        }
        """
        self.assertTrue(self.decryptor.is_messphp_file(messphp_content))
        
        # Negative test
        normal_php = "<?php echo 'Hello World'; ?>"
        self.assertFalse(self.decryptor.is_messphp_file(normal_php))
    
    def test_encrypted_payload_extraction(self):
        """Test extraction of encrypted payload"""
        test_content = '''eval($var("XqsgdgADQj++lPGZMyvkV0Kb3cIPGK4SpENmmEUVdkdd87NqMtJjfL9EVR43td33"));'''
        payload = self.decryptor._extract_encrypted_payload(test_content)
        self.assertIsNotNone(payload)
        self.assertTrue(len(payload) > 50)
    
    def test_key_extraction(self):
        """Test extraction of decryption key"""
        test_content = '''
        $key = xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii('048055057057053048054049056051098051053099098057101049099100097099099049100056101050054099053049');
        $xtea = new Xtea($key);
        '''
        key = self.decryptor._extract_key_from_code(test_content)
        self.assertIsNotNone(key)
    
    def test_php_code_cleaning(self):
        """Test PHP code cleaning and formatting"""
        messy_code = "\n\n\n<?php\n\n\necho 'test';\n\n\n"
        cleaned = self.decryptor.clean_php_code(messy_code)
        self.assertTrue(cleaned.startswith('<?php'))
        self.assertLess(cleaned.count('\n\n\n'), messy_code.count('\n\n\n'))


class TestMessPHPAnalyzer(unittest.TestCase):
    """Test cases for MessPHP analyzer"""
    
    def setUp(self):
        self.analyzer = MessPHPAnalyzer()
    
    def test_variable_pattern_detection(self):
        """Test detection of obfuscated variables"""
        test_content = """
        $m2118d22d991cc8bfb66304d5bd2ee973 = "test";
        $m6a4a7423907f51c2c734d4d465cc4547 = "another";
        $normal_var = "normal";
        """
        
        matches = self.analyzer.variable_patterns['obfuscated_vars'].findall(test_content)
        self.assertEqual(len(matches), 2)
        self.assertIn('$m2118d22d991cc8bfb66304d5bd2ee973', matches)
        self.assertIn('$m6a4a7423907f51c2c734d4d465cc4547', matches)
    
    def test_file_analysis(self):
        """Test complete file analysis"""
        messphp_content = """
        /* This file was protected by MessPHP v1.0 */
        $m2118d22d991cc8bfb66304d5bd2ee973 = xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii('088116101097');
        class Xtea {
            function block_decrypt() {}
        }
        eval($encrypted_data);
        """
        
        analysis = self.analyzer.analyze_file(messphp_content)
        
        self.assertTrue(analysis['is_messphp'])
        self.assertEqual(analysis['version'], '1.0')
        self.assertGreater(len(analysis['obfuscated_variables']), 0)
        self.assertGreater(analysis['complexity_score'], 0)
    
    def test_ascii_decoding(self):
        """Test ASCII string decoding in analyzer"""
        result = self.analyzer._decode_ascii_string("088116101097")
        self.assertEqual(result, "Xtea")
    
    def test_complexity_calculation(self):
        """Test complexity score calculation"""
        simple_analysis = {
            'is_messphp': True,
            'obfuscated_variables': [],
            'ascii_encoded_strings': [],
            'encrypted_payloads': [],
            'anti_debug_measures': []
        }
        
        complex_analysis = {
            'is_messphp': True,
            'obfuscated_variables': ['$m' + 'a' * 32] * 10,
            'ascii_encoded_strings': [{'encoded': '123', 'decoded': 'test'}] * 5,
            'encrypted_payloads': ['payload1', 'payload2'],
            'anti_debug_measures': ['measure1', 'measure2', 'measure3']
        }
        
        simple_score = self.analyzer._calculate_complexity("simple content", simple_analysis)
        complex_score = self.analyzer._calculate_complexity("complex content", complex_analysis)
        
        self.assertGreater(complex_score, simple_score)


class TestMessPHPValidator(unittest.TestCase):
    """Test cases for MessPHP validator"""
    
    def setUp(self):
        self.validator = MessPHPValidator()
    
    def test_php_syntax_validation(self):
        """Test PHP syntax validation"""
        # Valid PHP
        valid_php = "<?php echo 'Hello World'; ?>"
        is_valid, error = self.validator.validate_php_syntax(valid_php)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Invalid PHP - missing opening tag
        invalid_php = "echo 'Hello World';"
        is_valid, error = self.validator.validate_php_syntax(invalid_php)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # Invalid PHP - unbalanced braces
        unbalanced_php = "<?php if (true) { echo 'test'; ?>"
        is_valid, error = self.validator.validate_php_syntax(unbalanced_php)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_messphp_structure_validation(self):
        """Test MessPHP structure validation"""
        # Valid MessPHP structure
        valid_messphp = """
        class Xtea {
            function block_decrypt() {}
        }
        function xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii() {}
        eval($data);
        """
        
        is_valid, issues = self.validator.validate_messphp_structure(valid_messphp)
        self.assertTrue(is_valid)
        self.assertEqual(len(issues), 0)
        
        # Invalid MessPHP structure
        invalid_messphp = "<?php echo 'not messphp'; ?>"
        is_valid, issues = self.validator.validate_messphp_structure(invalid_messphp)
        self.assertFalse(is_valid)
        self.assertGreater(len(issues), 0)
    
    def test_decryption_success_estimation(self):
        """Test decryption success rate estimation"""
        # Good decryption result
        good_result = "<?php echo 'Hello World'; function test() { return true; }"
        success_rate = self.validator.estimate_decryption_success("original", good_result)
        self.assertGreater(success_rate, 0.5)
        
        # Poor decryption result
        poor_result = "gibberish!@#$%^&*()"
        success_rate = self.validator.estimate_decryption_success("original", poor_result)
        self.assertLess(success_rate, 0.5)


class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_sample_file_processing(self):
        """Test processing of sample MessPHP file"""
        if os.path.exists('sample_messphp.php'):
            decryptor = MessPHPDecryptor()
            
            with open('sample_messphp.php', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test detection
            self.assertTrue(decryptor.is_messphp_file(content))
            
            # Test payload extraction
            payload = decryptor._extract_encrypted_payload(content)
            self.assertIsNotNone(payload)
            
            # Note: Actual decryption might fail without the correct key
            # This is expected for the sample file
    
    def test_command_line_interface(self):
        """Test command-line interface functionality"""
        # This would require more complex mocking of sys.argv and file operations
        # For now, we'll just test that the main module can be imported
        try:
            import messphp_decryptor
            self.assertTrue(hasattr(messphp_decryptor, 'main'))
        except ImportError:
            self.fail("Could not import messphp_decryptor module")


def run_tests():
    """Run all tests"""
    # Create a test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestXTEADecryptor,
        TestMessPHPDecryptor,
        TestMessPHPAnalyzer,
        TestMessPHPValidator,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("MessPHP Decryptor Test Suite")
    print("=" * 40)
    
    success = run_tests()
    
    if success:
        print("\nAll tests passed! ✅")
        sys.exit(0)
    else:
        print("\nSome tests failed! ❌")
        sys.exit(1)
