# MessPHP Decryption Tutorial

This tutorial explains how to use the MessPHP Decryption Tool and understand the underlying mechanisms.

## Understanding MessPHP Protection

MessPHP is a PHP obfuscation tool that uses several techniques to protect PHP code:

### 1. Variable Name Obfuscation
All variable names are replaced with MD5 hashes:
```php
// Original: $username = "admin";
// Obfuscated: $m2118d22d991cc8bfb66304d5bd2ee973 = "admin";
```

### 2. String Encoding
Strings are encoded as ASCII codes using a custom function:
```php
// "Xtea" becomes "088116101097"
// "trim" becomes "116114105109"
// "encrypt" becomes "101110099114121112116"
```

### 3. XTEA Encryption
The main payload is encrypted using the XTEA (eXtended Tiny Encryption Algorithm):
- 64-bit block cipher
- 128-bit key
- 32 rounds of encryption
- CBC mode with initialization vector

### 4. Anti-Debugging Measures
- Checks for print/echo statements in the code
- Infinite loop traps
- Self-inspection mechanisms
- Base64 content validation

## Step-by-Step Decryption Process

### Step 1: Identify MessPHP Files
```bash
python messphp_decryptor.py suspicious_file.php --check
```

Look for these indicators:
- `/* This file was protected by MessPHP */` comment
- `class Xtea` definition
- `xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii` function
- Variables with MD5 hash names (`$m[a-f0-9]{32}`)

### Step 2: Analyze the Protection
```bash
python messphp_decryptor.py protected_file.php --analyze
```

This shows:
- MessPHP version
- Complexity score
- Number of obfuscated variables
- Anti-debugging measures
- Encrypted payloads

### Step 3: Extract Key Information
The tool automatically extracts:
- ASCII-encoded strings
- Decryption key from constructor
- Base64-encoded encrypted payload

### Step 4: Perform Decryption
```bash
python messphp_decryptor.py protected_file.php -o decrypted_file.php
```

## Manual Decryption Process

If automatic decryption fails, you can follow the manual process from the tutorial:

### 1. Change `eval` to `print`
```php
// Change this:
eval($m6a4a7423907f51c2c734d4d465cc4547($me570850cdc97d1d0b4000087eae8b8e8->$mdce2462bf288974f3cdad3ccf53bcfaa("encrypted_data")));

// To this:
print($m6a4a7423907f51c2c734d4d465cc4547($me570850cdc97d1d0b4000087eae8b8e8->$mdce2462bf288974f3cdad3ccf53bcfaa("encrypted_data")));
```

### 2. Modify Anti-Debug Check
Replace the complex conditional with:
```php
$m0d7d4a6c3a4b82a626f515a3e0ea2e38[] = array(
    $mafefa4846b0ba586edb703328cc3a8e1[0] ^ $m17a700bfdacd81b54034ba996377097e[$m86877db3fd52c024fabbc84075c443e6 - 2],
    $mafefa4846b0ba586edb703328cc3a8e1[1] ^ $m17a700bfdacd81b54034ba996377097e[$m86877db3fd52c024fabbc84075c443e6 - 1]
);
```

### 3. Clean the ASCII Decoder Function
Remove the anti-debug checks:
```php
function xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii($m74f51a33e1c412e4d00b78906d6e0c2f)
{
    $mebbc003b7fe27b2cf4dff8b7a332d39b = '';
    $m184966639caf361425b481dbebe88c5d = ceil(strlen($m74f51a33e1c412e4d00b78906d6e0c2f) / 3) * 3;
    $mf65300264d5b1d9370f2563e5e6ee006 = str_pad($m74f51a33e1c412e4d00b78906d6e0c2f, $m184966639caf361425b481dbebe88c5d, '0', STR_PAD_LEFT);
    for ($m86877db3fd52c024fabbc84075c443e6 = 0;$m86877db3fd52c024fabbc84075c443e6 < (strlen($mf65300264d5b1d9370f2563e5e6ee006) / 3);$m86877db3fd52c024fabbc84075c443e6++)
    {
        $mebbc003b7fe27b2cf4dff8b7a332d39b .= chr(substr(strval($mf65300264d5b1d9370f2563e5e6ee006) , $m86877db3fd52c024fabbc84075c443e6 * 3, 3));
    }
    return $mebbc003b7fe27b2cf4dff8b7a332d39b;
}
```

### 4. Run the Modified Script
Execute the modified PHP script to get the decrypted output.

## Common Issues and Solutions

### Issue 1: "Could not find encrypted payload"
**Cause**: The file might not be MessPHP protected or uses a different format.
**Solution**: 
- Use `--force` flag to attempt decryption anyway
- Check if the file is corrupted
- Look for alternative payload patterns

### Issue 2: "Could not determine decryption key"
**Cause**: The key extraction failed or uses a non-standard method.
**Solution**:
- Manually specify the key with `--key "your_key"`
- Try common keys: "", "0", "default", "messphp"
- Extract the key manually from the code

### Issue 3: Garbled Output
**Cause**: Wrong key, corrupted data, or different XTEA implementation.
**Solution**:
- Verify the key is correct
- Check if the file uses a different encryption variant
- Try different key formats (string vs array)

### Issue 4: PHP Syntax Errors in Output
**Cause**: Partial decryption failure or encoding issues.
**Solution**:
- Check the original file integrity
- Validate the decryption key
- Look for encoding issues (UTF-8 vs Latin-1)

## Advanced Usage

### Custom Key Extraction
```python
# Extract key manually
with open('protected_file.php', 'r') as f:
    content = f.read()

# Look for key patterns
import re
key_pattern = r"new.*?Xtea\([^'\"]*['\"]([^'\"]+)['\"]"
match = re.search(key_pattern, content, re.IGNORECASE)
if match:
    key = match.group(1)
    print(f"Found key: {key}")
```

### Batch Processing
```bash
# Process all PHP files in a directory
find /path/to/files -name "*.php" -exec python messphp_decryptor.py {} --check \;

# Decrypt all MessPHP files to a directory
python messphp_decryptor.py /path/to/files/*.php -o /path/to/decrypted/
```

### Integration with Other Tools
```python
from messphp_decryptor import MessPHPDecryptor
from messphp_utils import MessPHPAnalyzer

# Use in your own scripts
decryptor = MessPHPDecryptor()
analyzer = MessPHPAnalyzer()

with open('file.php', 'r') as f:
    content = f.read()

if decryptor.is_messphp_file(content):
    analysis = analyzer.analyze_file(content)
    decrypted = decryptor.decrypt_content(content)
    print(f"Decrypted {len(decrypted)} characters")
```

## Security Considerations

1. **Legal Use Only**: Only use this tool on files you own or have permission to analyze
2. **Malware Analysis**: Be cautious when analyzing unknown PHP files
3. **Sandboxing**: Run decryption in isolated environments for untrusted files
4. **Backup**: Always keep backups of original files before modification

## Contributing

To contribute to this project:

1. Understand the MessPHP protection mechanisms
2. Test with various MessPHP variants
3. Add support for new obfuscation techniques
4. Improve error handling and user experience
5. Add comprehensive test cases

## References

- [Original Tutorial](https://github.com/drubicza/tutorials/blob/master/reversing/tutorial-dekripsi_proteksi_messphp.md)
- [XTEA Algorithm](https://en.wikipedia.org/wiki/XTEA)
- [PHP Obfuscation Techniques](https://www.php.net/manual/en/security.hiding.php)
