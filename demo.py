#!/usr/bin/env python3
"""
MessPHP Decryptor Demo

This script demonstrates the basic functionality of the MessPHP decryptor.
"""

import os
import sys
from messphp_decryptor import MessPHPDecryptor, XTEADecryptor
from messphp_utils import <PERSON>ssP<PERSON>Anal<PERSON><PERSON>, MessPHPValidator, create_decryption_report


def demo_ascii_decoding():
    """Demonstrate ASCII string decoding"""
    print("=== ASCII String Decoding Demo ===")
    
    decryptor = MessPHPDecryptor()
    
    test_cases = [
        ("088116101097", "Xtea"),
        ("116114105109", "trim"),
        ("101110099114121112116", "encrypt"),
        ("048055057057053048054049056051098051053099098057101049099100097099099049100056101050054099053049", "0//50419835359e1cda991d8e264c51")
    ]
    
    for encoded, expected in test_cases:
        decoded = decryptor._decode_ascii_string(encoded)
        status = "✅" if decoded == expected else "❌"
        print(f"{status} '{encoded}' -> '{decoded}' (expected: '{expected}')")
    
    print()


def demo_xtea_operations():
    """Demonstrate XTEA operations"""
    print("=== XTEA Operations Demo ===")
    
    # Test with different keys
    keys = ["", "test", "messphp", [1, 2, 3, 4]]
    
    for key in keys:
        xtea = XTEADecryptor(key)
        print(f"Key: {key} -> XTEA key: {xtea.key}")
    
    # Test 32-bit operations
    xtea = XTEADecryptor("test")
    print(f"32-bit add test: {xtea._add(0x7FFFFFFF, 1)}")
    print(f"Right shift test: {xtea._rshift(0x80000000, 1)}")
    
    print()


def demo_messphp_detection():
    """Demonstrate MessPHP file detection"""
    print("=== MessPHP Detection Demo ===")
    
    decryptor = MessPHPDecryptor()
    
    # Test with sample file if it exists
    if os.path.exists('sample_messphp.php'):
        with open('sample_messphp.php', 'r', encoding='utf-8') as f:
            content = f.read()
        
        is_messphp = decryptor.is_messphp_file(content)
        print(f"✅ sample_messphp.php: {'MessPHP protected' if is_messphp else 'Not MessPHP protected'}")
        
        # Extract payload
        payload = decryptor._extract_encrypted_payload(content)
        if payload:
            print(f"✅ Encrypted payload found: {len(payload)} characters")
            print(f"   Preview: {payload[:50]}...")
        else:
            print("❌ No encrypted payload found")
        
        # Extract key
        key = decryptor._extract_key_from_code(content)
        if key:
            print(f"✅ Decryption key found: '{key}'")
        else:
            print("❌ No decryption key found")
    else:
        print("❌ sample_messphp.php not found")
    
    print()


def demo_analysis():
    """Demonstrate MessPHP analysis"""
    print("=== MessPHP Analysis Demo ===")
    
    if not MessPHPAnalyzer:
        print("❌ MessPHPAnalyzer not available")
        return
    
    analyzer = MessPHPAnalyzer()
    
    if os.path.exists('sample_messphp.php'):
        with open('sample_messphp.php', 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = analyzer.analyze_file(content)
        
        print(f"MessPHP Protected: {'Yes' if analysis['is_messphp'] else 'No'}")
        print(f"Version: {analysis.get('version', 'Unknown')}")
        print(f"Complexity Score: {analysis['complexity_score']}/100")
        print(f"Obfuscated Variables: {len(analysis['obfuscated_variables'])}")
        print(f"ASCII Encoded Strings: {len(analysis['ascii_encoded_strings'])}")
        print(f"Encrypted Payloads: {len(analysis['encrypted_payloads'])}")
        print(f"Anti-Debug Measures: {len(analysis['anti_debug_measures'])}")
        
        if analysis['anti_debug_measures']:
            print("Anti-Debug Measures:")
            for measure in analysis['anti_debug_measures']:
                print(f"  - {measure}")
        
        # Show some decoded strings
        if analysis['ascii_encoded_strings']:
            print("\nDecoded ASCII Strings:")
            for item in analysis['ascii_encoded_strings'][:5]:
                print(f"  '{item['encoded']}' -> '{item['decoded']}'")
    else:
        print("❌ sample_messphp.php not found")
    
    print()


def demo_validation():
    """Demonstrate PHP validation"""
    print("=== PHP Validation Demo ===")
    
    if not MessPHPValidator:
        print("❌ MessPHPValidator not available")
        return
    
    validator = MessPHPValidator()
    
    test_codes = [
        ("<?php echo 'Hello World'; ?>", "Valid PHP"),
        ("echo 'Hello World';", "Missing PHP tag"),
        ("<?php if (true) { echo 'test'; ?>", "Unbalanced braces"),
        ("<?php function test() { return true; } ?>", "Valid PHP with function")
    ]
    
    for code, description in test_codes:
        is_valid, error = validator.validate_php_syntax(code)
        status = "✅" if is_valid else "❌"
        print(f"{status} {description}: {'Valid' if is_valid else error}")
    
    print()


def demo_full_decryption():
    """Demonstrate full decryption process"""
    print("=== Full Decryption Demo ===")
    
    if not os.path.exists('sample_messphp.php'):
        print("❌ sample_messphp.php not found - creating a simple test case")
        return
    
    decryptor = MessPHPDecryptor()
    
    try:
        with open('sample_messphp.php', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 Analyzing file...")
        is_messphp = decryptor.is_messphp_file(content)
        print(f"   MessPHP protected: {'Yes' if is_messphp else 'No'}")
        
        if is_messphp:
            print("🔓 Attempting decryption...")
            try:
                decrypted = decryptor.decrypt_content(content)
                cleaned = decryptor.clean_php_code(decrypted)
                
                print("✅ Decryption successful!")
                print(f"   Decrypted code length: {len(cleaned)} characters")
                print("   Preview:")
                print("   " + "-" * 50)
                for line in cleaned.split('\n')[:10]:
                    print(f"   {line}")
                if len(cleaned.split('\n')) > 10:
                    print("   ...")
                print("   " + "-" * 50)
                
                # Validate result
                if MessPHPValidator:
                    validator = MessPHPValidator()
                    is_valid, error = validator.validate_php_syntax(cleaned)
                    print(f"   PHP syntax valid: {'Yes' if is_valid else 'No'}")
                    if error:
                        print(f"   Error: {error}")
                
            except Exception as e:
                print(f"❌ Decryption failed: {str(e)}")
                print("   This is expected for the sample file without the correct key")
        
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")
    
    print()


def main():
    """Run all demos"""
    print("MessPHP Decryptor Demo")
    print("=" * 50)
    print()
    
    demos = [
        demo_ascii_decoding,
        demo_xtea_operations,
        demo_messphp_detection,
        demo_analysis,
        demo_validation,
        demo_full_decryption
    ]
    
    for demo in demos:
        try:
            demo()
        except Exception as e:
            print(f"❌ Demo failed: {str(e)}")
            print()
    
    print("Demo completed!")
    print("\nTo try the full decryptor, run:")
    print("python messphp_decryptor.py sample_messphp.php --analyze")
    print("python messphp_decryptor.py sample_messphp.php --check")


if __name__ == '__main__':
    main()
