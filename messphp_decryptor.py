#!/usr/bin/env python3
"""
MessPHP Decryption Tool

A comprehensive tool for decrypting MessPHP obfuscated/encrypted PHP files.
Based on the tutorial: https://github.com/drubicza/tutorials/blob/master/reversing/tutorial-dekripsi_proteksi_messphp.md

Author: MessPHP Decryptor
Version: 1.0
"""

import re
import base64
import struct
import argparse
import sys
import os
import glob
from typing import List, Tuple, Optional, Union

try:
    from messphp_utils import MessPHPAnalyzer, MessPHPValidator, create_decryption_report
except ImportError:
    # Fallback if utils module is not available
    MessPHPAnalyzer = None
    MessPHPValidator = None
    create_decryption_report = None


class XTEADecryptor:
    """XTEA decryption implementation for MessPHP"""
    
    def __init__(self, key: Union[str, List[int]]):
        """Initialize XTEA with key"""
        self.key = self._setup_key(key)
        self.cbc = True
    
    def _setup_key(self, key: Union[str, List[int]]) -> List[int]:
        """Setup encryption key"""
        if isinstance(key, list):
            return key
        elif isinstance(key, str) and key:
            # Pad key to 16 bytes and convert to 32-bit integers
            padded_key = (key * ((16 // len(key)) + 1))[:16]
            return list(struct.unpack('>4I', padded_key.encode('latin-1')[:16]))
        else:
            return [0, 0, 0, 0]
    
    def _add(self, *args) -> int:
        """32-bit addition with overflow handling"""
        result = sum(args)
        # Handle 32-bit overflow
        if result > 0xFFFFFFFF or result < -0xFFFFFFFF:
            result = result % (0xFFFFFFFF + 1)
        
        if result > 0x7FFFFFFF:
            result -= 0xFFFFFFFF + 1
        elif result < -0x80000000:
            result += 0xFFFFFFFF + 1
            
        return int(result)
    
    def _rshift(self, value: int, shift: int) -> int:
        """Right shift with proper handling of negative numbers"""
        # Normalize to 32-bit range
        if value > 0xFFFFFFFF or value < -0xFFFFFFFF:
            value = value % (0xFFFFFFFF + 1)
        
        if value > 0x7FFFFFFF:
            value -= 0xFFFFFFFF + 1
        elif value < -0x80000000:
            value += 0xFFFFFFFF + 1
        
        if value < 0:
            value &= 0x7FFFFFFF
            value >>= shift
            value |= 1 << (31 - shift)
        else:
            value >>= shift
            
        return value
    
    def _block_decrypt(self, v0: int, v1: int) -> Tuple[int, int]:
        """Decrypt a single 64-bit block using XTEA"""
        delta = 0x9E3779B9
        sum_val = 0xC6EF3720  # delta * 32
        
        for _ in range(32):
            v1 = self._add(v1, -(self._add(
                (v0 << 4) ^ self._rshift(v0, 5), v0
            ) ^ self._add(sum_val, self.key[self._rshift(sum_val, 11) & 3])))
            
            sum_val = self._add(sum_val, -delta)
            
            v0 = self._add(v0, -(self._add(
                (v1 << 4) ^ self._rshift(v1, 5), v1
            ) ^ self._add(sum_val, self.key[sum_val & 3])))
        
        return v0, v1
    
    def _str_to_longs(self, data: str) -> List[int]:
        """Convert string to array of 32-bit integers"""
        # Pad to multiple of 4 bytes
        padded_len = ((len(data) + 3) // 4) * 4
        padded_data = data.ljust(padded_len, '\x00')
        
        longs = []
        for i in range(0, len(padded_data), 4):
            chunk = padded_data[i:i+4]
            if len(chunk) == 4:
                longs.append(struct.unpack('>I', chunk.encode('latin-1'))[0])
        
        return longs
    
    def _longs_to_str(self, longs: List[int]) -> str:
        """Convert array of 32-bit integers to string"""
        result = ""
        for long_val in longs:
            result += struct.pack('>I', long_val & 0xFFFFFFFF).decode('latin-1')
        return result
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt base64 encoded XTEA encrypted data"""
        try:
            # Decode base64
            decoded = base64.b64decode(encrypted_data).decode('latin-1')
            
            # Convert to 32-bit integers
            longs = self._str_to_longs(decoded)
            
            if len(longs) < 2:
                raise ValueError("Insufficient data for decryption")
            
            decrypted_blocks = []
            
            # Skip first 2 longs if CBC mode (IV)
            start_idx = 2 if self.cbc else 0
            
            for i in range(start_idx, len(longs), 2):
                if i + 1 < len(longs):
                    v0, v1 = self._block_decrypt(longs[i], longs[i + 1])
                    
                    # Apply CBC XOR if enabled
                    if self.cbc and i >= 2:
                        v0 ^= longs[i - 2]
                        v1 ^= longs[i - 1]
                    
                    decrypted_blocks.extend([v0, v1])
            
            # Convert back to string
            result = self._longs_to_str(decrypted_blocks)
            return result.rstrip('\x00 ')
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")


class MessPHPDecryptor:
    """Main MessPHP decryption class"""
    
    def __init__(self):
        self.messphp_pattern = re.compile(
            r'/\* This file was protected by MessPHP.*?\*/',
            re.DOTALL | re.IGNORECASE
        )
        
    def _decode_ascii_string(self, ascii_codes: str) -> str:
        """Decode ASCII codes string to characters (xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii function)"""
        try:
            # Pad to multiple of 3
            padded_len = ((len(ascii_codes) + 2) // 3) * 3
            padded_codes = ascii_codes.zfill(padded_len)
            
            result = ""
            for i in range(0, len(padded_codes), 3):
                code = int(padded_codes[i:i+3])
                if 0 <= code <= 255:
                    result += chr(code)
            
            return result
        except (ValueError, OverflowError):
            return ""
    
    def _extract_encrypted_payload(self, content: str) -> Optional[str]:
        """Extract the base64 encrypted payload from MessPHP code"""
        # Look for the encrypted string in the eval statement
        eval_pattern = r'eval\([^"]*"([A-Za-z0-9+/=]+)"[^)]*\)'
        match = re.search(eval_pattern, content)
        
        if match:
            return match.group(1)
        
        # Alternative pattern for different MessPHP variants
        alt_pattern = r'->encrypt\("([A-Za-z0-9+/=]+)"\)'
        match = re.search(alt_pattern, content)
        
        if match:
            return match.group(1)
            
        return None

    def _extract_key_from_code(self, content: str) -> Optional[str]:
        """Extract the decryption key from MessPHP code"""
        # Look for the key in the Xtea constructor
        key_pattern = r"new.*?Xtea\([^'\"]*['\"]([^'\"]+)['\"]"
        match = re.search(key_pattern, content, re.IGNORECASE)

        if match:
            key_encoded = match.group(1)
            # Try to decode if it's ASCII encoded
            if key_encoded.isdigit():
                return self._decode_ascii_string(key_encoded)
            return key_encoded

        # Look for encoded key in variable assignment - more flexible pattern
        # This pattern looks for any function name that takes a numeric string parameter
        encoded_key_pattern = r"\$\w+\s*=\s*[a-zA-Z_][a-zA-Z0-9_]*\(['\"](\d+)['\"]\)"
        match = re.search(encoded_key_pattern, content)

        if match:
            return self._decode_ascii_string(match.group(1))

        # Also try to find the key in the Xtea constructor with function call
        # Pattern: new Xtea(functionName('digits'))
        xtea_with_function_pattern = r"new.*?Xtea\([^(]*[a-zA-Z_][a-zA-Z0-9_]*\(['\"](\d+)['\"]\)"
        match = re.search(xtea_with_function_pattern, content, re.IGNORECASE)

        if match:
            return self._decode_ascii_string(match.group(1))

        return None

    def is_messphp_file(self, content: str) -> bool:
        """Check if the content is a MessPHP protected file"""
        indicators = [
            "This file was protected by MessPHP",
            "class Xtea",
            "xKUgYxiuPUSYsaVJejCobGnbCHOxLyiii",
            "block_decrypt",
            "block_encrypt"
        ]

        return any(indicator in content for indicator in indicators)

    def decrypt_file(self, file_path: str) -> str:
        """Decrypt a MessPHP protected file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            raise ValueError(f"Failed to read file: {str(e)}")

        return self.decrypt_content(content)

    def decrypt_content(self, content: str) -> str:
        """Decrypt MessPHP protected content"""
        if not self.is_messphp_file(content):
            raise ValueError("This doesn't appear to be a MessPHP protected file")

        # Extract the encrypted payload
        encrypted_payload = self._extract_encrypted_payload(content)
        if not encrypted_payload:
            raise ValueError("Could not find encrypted payload in MessPHP file")

        # Extract the decryption key
        key = self._extract_key_from_code(content)
        if not key:
            # Try common default keys
            common_keys = ["", "0", "default", "messphp"]
            for test_key in common_keys:
                try:
                    xtea = XTEADecryptor(test_key)
                    result = xtea.decrypt(encrypted_payload)
                    if result and "<?php" in result:
                        return result
                except:
                    continue
            raise ValueError("Could not determine decryption key")

        # Decrypt the payload
        try:
            xtea = XTEADecryptor(key)
            decrypted = xtea.decrypt(encrypted_payload)

            if not decrypted:
                raise ValueError("Decryption produced empty result")

            return decrypted

        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")

    def clean_php_code(self, code: str) -> str:
        """Clean and format the decrypted PHP code"""
        # Remove extra whitespace and normalize line endings
        code = re.sub(r'\r\n|\r', '\n', code)
        code = re.sub(r'\n\s*\n', '\n\n', code)

        # Ensure proper PHP opening tag
        if not code.strip().startswith('<?php'):
            code = '<?php\n' + code

        return code.strip()


def main():
    """Main function for command-line interface"""
    parser = argparse.ArgumentParser(
        description="MessPHP Decryption Tool - Decrypt MessPHP obfuscated PHP files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s input.php                    # Decrypt and print to stdout
  %(prog)s input.php -o output.php      # Decrypt and save to file
  %(prog)s input.php --check            # Check if file is MessPHP protected
  %(prog)s input.php --analyze          # Analyze MessPHP protection details
  %(prog)s *.php -o decrypted/          # Decrypt multiple files to directory
  %(prog)s input.php --report           # Generate detailed decryption report
        """
    )

    parser.add_argument('input', nargs='+', help='Input MessPHP file(s) to decrypt')
    parser.add_argument('-o', '--output', help='Output file or directory')
    parser.add_argument('--check', action='store_true',
                       help='Only check if files are MessPHP protected')
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze MessPHP protection details')
    parser.add_argument('--report', action='store_true',
                       help='Generate detailed decryption report')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output')
    parser.add_argument('--force', action='store_true',
                       help='Force decryption even if file doesn\'t look like MessPHP')
    parser.add_argument('--key', help='Specify decryption key manually')

    args = parser.parse_args()

    decryptor = MessPHPDecryptor()
    analyzer = MessPHPAnalyzer() if MessPHPAnalyzer else None
    validator = MessPHPValidator() if MessPHPValidator else None
    success_count = 0
    total_count = len(args.input)

    for input_file in args.input:
        if not os.path.exists(input_file):
            print(f"Error: File '{input_file}' not found", file=sys.stderr)
            continue

        try:
            if args.verbose:
                print(f"Processing: {input_file}")

            # Read file content
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Check if MessPHP protected
            is_messphp = decryptor.is_messphp_file(content)

            # Analyze file if requested
            analysis = None
            if analyzer and (args.analyze or args.report):
                analysis = analyzer.analyze_file(content)

            if args.check:
                status = "MessPHP protected" if is_messphp else "Not MessPHP protected"
                if analysis:
                    status += f" (Complexity: {analysis['complexity_score']}/100)"
                print(f"{input_file}: {status}")
                continue

            if args.analyze:
                if analysis:
                    print(f"\nAnalysis for {input_file}:")
                    print(f"MessPHP Protected: {'Yes' if analysis['is_messphp'] else 'No'}")
                    print(f"Version: {analysis.get('version', 'Unknown')}")
                    print(f"Complexity Score: {analysis['complexity_score']}/100")
                    print(f"Obfuscated Variables: {len(analysis['obfuscated_variables'])}")
                    print(f"ASCII Encoded Strings: {len(analysis['ascii_encoded_strings'])}")
                    print(f"Encrypted Payloads: {len(analysis['encrypted_payloads'])}")
                    print(f"Anti-Debug Measures: {len(analysis['anti_debug_measures'])}")
                    if analysis['anti_debug_measures']:
                        print("Anti-Debug Measures:")
                        for measure in analysis['anti_debug_measures']:
                            print(f"  - {measure}")
                else:
                    print(f"Analysis not available for {input_file}")
                continue

            if not is_messphp and not args.force:
                print(f"Warning: '{input_file}' doesn't appear to be MessPHP protected. Use --force to decrypt anyway.", file=sys.stderr)
                continue

            # Decrypt the file
            if args.key:
                # Use manually specified key
                from messphp_decryptor import XTEADecryptor
                encrypted_payload = decryptor._extract_encrypted_payload(content)
                if not encrypted_payload:
                    raise ValueError("Could not find encrypted payload")
                xtea = XTEADecryptor(args.key)
                decrypted = xtea.decrypt(encrypted_payload)
            else:
                decrypted = decryptor.decrypt_content(content)

            cleaned = decryptor.clean_php_code(decrypted)

            # Generate report if requested
            if args.report and analysis and create_decryption_report:
                success_rate = validator.estimate_decryption_success(content, cleaned) if validator else 0.8
                report = create_decryption_report(input_file, analysis, cleaned, success_rate)

                if args.output:
                    report_file = f"{args.output}.report" if not os.path.isdir(args.output) else os.path.join(args.output, f"{os.path.basename(input_file)}.report")
                    with open(report_file, 'w', encoding='utf-8') as f:
                        f.write(report)
                    if args.verbose:
                        print(f"Report saved to: {report_file}")
                else:
                    print(report)
                    print("\n" + "="*50 + "\n")

            # Handle output
            if args.output and not args.report:
                if os.path.isdir(args.output):
                    # Output directory specified
                    output_file = os.path.join(args.output, f"decrypted_{os.path.basename(input_file)}")
                elif total_count == 1:
                    # Single file, specific output file
                    output_file = args.output
                else:
                    # Multiple files, but single output file specified - error
                    print(f"Error: Cannot write multiple files to single output file '{args.output}'", file=sys.stderr)
                    continue

                # Create output directory if needed
                os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned)

                if args.verbose:
                    print(f"Decrypted content saved to: {output_file}")
            elif not args.report:
                # Print to stdout
                print(cleaned)

            success_count += 1

        except Exception as e:
            print(f"Error processing '{input_file}': {str(e)}", file=sys.stderr)

    if args.verbose and not args.check and not args.analyze:
        print(f"\nDecryption completed: {success_count}/{total_count} files processed successfully")

    return 0 if success_count > 0 else 1


if __name__ == "__main__":
    sys.exit(main())
